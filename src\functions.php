<?php

// Configuration: Base URL for the application
// Change this if you're running on a different host/port
define('BASE_URL', 'http://localhost:8080');
// Path to the src directory from web root (empty if server runs from src directory)
define('SRC_PATH', '');

/**
 * Generate a 6-digit numeric verification code.
 */
function generateVerificationCode(): string
{
  return str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);
}

/**
 * Send a verification code to an email.
 */
function sendVerificationEmail(string $email, string $code, string $type = 'verification'): bool
{
  ini_set("SMTP", "localhost");
  ini_set("smtp_port", "1025");
  ini_set("sendmail_from", "<EMAIL>");

  if ($type === 'unsubscribe') {
    $subject = "Confirm Unsubscription";
    $message = "<p>To confirm unsubscription, use this code: <strong>$code</strong></p>";
  } else {
    $subject = "Your Verification Code";
    $message = "<p>Your verification code is: <strong>$code</strong></p>";
  }

  $headers = "From: <EMAIL>\r\n";
  $headers .= "Content-type: text/html\r\n";

  return mail($email, $subject, $message, $headers);
}

/**
 * Register an email by storing it in a file.
 */
function registerEmail(string $email): bool
{
  $file = __DIR__ . '/registered_emails.txt';

  // Use file locking to prevent race conditions
  $fp = fopen($file, 'c+');
  if (!$fp || !flock($fp, LOCK_EX)) {
    if ($fp) fclose($fp);
    error_log("Failed to acquire file lock for registration: " . $email);
    return false;
  }

  // Read existing emails
  $content = stream_get_contents($fp);
  $emails = array_filter(array_map('trim', explode("\n", $content)));

  if (in_array($email, $emails)) {
    flock($fp, LOCK_UN);
    fclose($fp);
    error_log("Email already registered: " . $email);
    return false; // Email already registered
  }

  // Append new email
  fseek($fp, 0, SEEK_END);
  fwrite($fp, $email . PHP_EOL);
  flock($fp, LOCK_UN);
  fclose($fp);
  error_log("Successfully registered email: " . $email);
  return true;
}



/**
 * Unsubscribe an email by removing it from the list.
 */
function unsubscribeEmail(string $email): bool
{
  $file = __DIR__ . '/registered_emails.txt';
  if (!file_exists($file)) {
    return false;
  }

  $emails = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
  $filtered = array_filter($emails, fn($e) => trim($e) !== $email);

  if (count($emails) === count($filtered)) {
    return false; // Email not found
  }

  file_put_contents($file, implode(PHP_EOL, $filtered) . PHP_EOL);
  return true;
}

/**
 * Fetch GitHub timeline.
 */
function fetchGitHubTimeline()
{
  $url = 'https://www.github.com/timeline';

  // Try cURL first if available
  if (function_exists('curl_init')) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_USERAGENT, 'GitHubTimelineApp/1.0');
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Disable SSL verification for local testing

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
      error_log("cURL Error: " . $error);
    } else if ($httpCode !== 200) {
      error_log("GitHub API returned HTTP " . $httpCode);
    } else {
      return $result;
    }
  }

  // Fallback to file_get_contents if cURL fails or is not available
  error_log("Falling back to file_get_contents for GitHub API");

  $context = stream_context_create([
    'http' => [
      'method' => 'GET',
      'header' => [
        'User-Agent: GitHubTimelineApp/1.0',
        'Accept: application/json'
      ],
      'timeout' => 30
    ],
    'ssl' => [
      'verify_peer' => false,
      'verify_peer_name' => false
    ]
  ]);

  $result = @file_get_contents($url, false, $context);

  if ($result === false) {
    error_log("Failed to fetch GitHub timeline with file_get_contents");

    // Return mock data for testing if API fails
    return json_encode([
      [
        'id' => 'test_' . time(),
        'type' => 'PushEvent',
        'actor' => ['login' => 'testuser'],
        'repo' => ['name' => 'testuser/test-repo'],
        'created_at' => date('c')
      ],
      [
        'id' => 'test_' . (time() + 1),
        'type' => 'CreateEvent',
        'actor' => ['login' => 'anotheruser'],
        'repo' => ['name' => 'anotheruser/demo-project'],
        'created_at' => date('c', time() - 300)
      ]
    ]);
  }

  return $result;
}

/**
 * Format GitHub timeline data. Returns a valid HTML string.
 */
function formatGitHubData(array $data): string
{
  if (empty($data)) {
    return "<h2>GitHub Timeline Updates</h2><table border=\"1\"><tr><th>Event</th><th>User</th></tr><tr><td>No events</td><td>N/A</td></tr></table>";
  }

  // Required format from README
  $html = "<h2>GitHub Timeline Updates</h2>";
  $html .= "<table border=\"1\">";
  $html .= "<tr><th>Event</th><th>User</th></tr>";

  $limitedData = array_slice($data, 0, 10);

  foreach ($limitedData as $item) {
    $event = htmlspecialchars($item['type'] ?? 'Unknown');
    $user = htmlspecialchars($item['actor']['login'] ?? 'testuser');

    $html .= "<tr><td>$event</td><td>$user</td></tr>";
  }

  $html .= "</table>";
  return $html;
}

/**
 * Send the formatted GitHub updates to registered emails.
 */
function sendGitHubUpdatesToSubscribers(): void
{
  $file = __DIR__ . '/registered_emails.txt';

  if (!file_exists($file)) {
    error_log("No registered emails file found");
    return;
  }

  $emails = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
  if (empty($emails)) {
    error_log("No emails found in registered emails file");
    return;
  }

  // Fetch GitHub data
  $data = fetchGitHubTimeline();
  if ($data === false) {
    error_log("Failed to fetch GitHub timeline");
    return;
  }

  $decodedData = json_decode($data, true);
  if (!is_array($decodedData)) {
    error_log("Failed to decode GitHub timeline JSON");
    return;
  }

  // Check for new updates to avoid sending duplicate emails
  $lastRunFile = __DIR__ . '/last_run_data.json';
  $isNew = true;

  if (file_exists($lastRunFile)) {
    $lastData = json_decode(file_get_contents($lastRunFile), true);
    if (!empty($decodedData) && !empty($lastData)) {
      $isNew = ($decodedData[0]['id'] ?? '') !== ($lastData[0]['id'] ?? '');
    }
  }

  if (!$isNew) {
    error_log("No new updates found, skipping email send");
    return;
  }

  // Save new data for next time
  file_put_contents($lastRunFile, json_encode($decodedData));

  // Format HTML and send email
  $html = formatGitHubData($decodedData);
  $emailsSent = 0;

  // Configure SMTP settings for email sending
  ini_set("SMTP", "localhost");
  ini_set("smtp_port", "1025");
  ini_set("sendmail_from", "<EMAIL>");

  foreach ($emails as $email) {
    $email = trim($email);
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
      error_log("Invalid email address: $email");
      continue;
    }

    // Generate dynamic unsubscribe URL based on server configuration
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost:8080';
    $baseUrl = $protocol . '://' . $host;

    // If running from CLI (cron), fall back to configured BASE_URL
    if (php_sapi_name() === 'cli') {
      $baseUrl = BASE_URL;
    }

    // Include the src path in the URL
    $unsubscribeLink = $baseUrl . SRC_PATH . "/unsubscribe.php?email=" . urlencode($email);
    $body = $html . "<p><a href=\"$unsubscribeLink\" id=\"unsubscribe-button\">Unsubscribe</a></p>";

    $headers = "From: <EMAIL>\r\n";
    $headers .= "Content-type: text/html; charset=UTF-8\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";

    if (mail($email, "Latest GitHub Updates", $body, $headers)) {
      $emailsSent++;
      error_log("Email sent successfully to: $email");
    } else {
      error_log("Failed to send email to: $email");
    }
  }

  error_log("Successfully sent emails to $emailsSent recipients");
}
