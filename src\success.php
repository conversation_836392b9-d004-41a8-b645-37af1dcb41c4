<?php
session_start();

error_log("success.php accessed");
error_log("Session ID: " . session_id());
error_log("success_email in session: " . (isset($_SESSION['success_email']) ? $_SESSION['success_email'] : 'NOT SET'));

// Check if user came from successful registration
if (!isset($_SESSION['success_email'])) {
    error_log("No success_email in session, redirecting to index.php");
    header('Location: index.php');
    exit();
}

$email = $_SESSION['success_email'];
error_log("Success page loaded for email: " . $email);
// Clear the success session data
unset($_SESSION['success_email']);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Registration Successful</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
        :root {
            --primary: #0d6efd;
            --success: #198754;
            --text: #1f2937;
            --radius: 12px;
            --transition: 0.3s ease;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f6f8;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .success-card {
            background-color: #ffffff;
            padding: 40px 35px;
            border-radius: var(--radius);
            box-shadow: 0 8px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 420px;
            text-align: center;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            0% {
                opacity: 0;
                transform: translateY(-20px);
            }

            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-icon {
            font-size: 4rem;
            color: var(--success);
            margin-bottom: 20px;
            animation: bounce 0.6s ease-in-out;
        }

        @keyframes bounce {

            0%,
            20%,
            50%,
            80%,
            100% {
                transform: translateY(0);
            }

            40% {
                transform: translateY(-10px);
            }

            60% {
                transform: translateY(-5px);
            }
        }

        h1 {
            color: var(--success);
            font-size: 1.8rem;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .success-message {
            color: var(--text);
            font-size: 1.1rem;
            margin-bottom: 10px;
            line-height: 1.5;
        }

        .email-display {
            background-color: #f8f9fa;
            padding: 12px 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            color: var(--primary);
            font-weight: 600;
            margin: 20px 0;
            border: 2px solid #e9ecef;
        }

        .info-text {
            color: #6c757d;
            font-size: 0.95rem;
            margin-bottom: 30px;
            line-height: 1.4;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: var(--primary);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 500;
            transition: background-color var(--transition);
            margin: 0 10px;
        }

        .btn:hover {
            background-color: #0b5ed7;
        }

        .btn-secondary {
            background-color: #6c757d;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .button-group {
            margin-top: 20px;
        }

        @media (max-width: 500px) {
            .success-card {
                padding: 30px 20px;
                margin: 20px;
            }

            h1 {
                font-size: 1.5rem;
            }

            .btn {
                display: block;
                margin: 10px 0;
                text-align: center;
            }
        }
    </style>
</head>

<body>
    <div class="success-card">
        <div class="success-icon">✅</div>

        <h1>Registration Successful!</h1>

        <p class="success-message">
            Your email has been verified and successfully registered for GitHub timeline updates.
        </p>

        <div class="email-display">
            <?= htmlspecialchars($email) ?>
        </div>

        <p class="info-text">
            You will now receive GitHub timeline updates every 5 minutes.
            Each email will include an unsubscribe link if you wish to opt out later.
        </p>

        <div class="button-group">
            <a href="index.php" class="btn">Register Another Email</a>
            <?php
            // Generate dynamic unsubscribe URL
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'] ?? 'localhost:8080';
            $baseUrl = $protocol . '://' . $host;
            // Get the current script's directory path relative to document root
            $currentDir = dirname($_SERVER['SCRIPT_NAME']);
            $unsubscribeUrl = $baseUrl . $currentDir . '/unsubscribe.php?email=' . urlencode($email);
            ?>
            <a href="<?= $unsubscribeUrl ?>" class="btn btn-secondary">Unsubscribe</a>
        </div>
    </div>
</body>

</html>