<?php
require_once 'functions.php';
session_start();

$message = '';
$step = 1;

// Email should be manually entered as per README requirements

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['unsubscribe_email'])) {
        $email = $_POST['unsubscribe_email'];
        $_SESSION['unsubscribe_email'] = $email;
        $code = generateVerificationCode();
        $_SESSION['unsubscribe_code'] = $code;
        if (sendUnsubscribeEmail($email, $code)) {
            $message = "<p class='success'>Unsubscribe verification code sent to $email</p>";
            $step = 2;
        } else {
            $message = "<p class='error'>Failed to send verification code. Please try again.</p>";
        }
    }

    if (isset($_POST['unsubscribe_verification_code'])) {
        if ($_POST['unsubscribe_verification_code'] === $_SESSION['unsubscribe_code']) {
            if (unsubscribeEmail($_SESSION['unsubscribe_email'])) {
                $_SESSION['unsubscribe_success_email'] = $_SESSION['unsubscribe_email'];
                // Clear session data after successful unsubscription
                unset($_SESSION['unsubscribe_email']);
                unset($_SESSION['unsubscribe_code']);
                // Redirect to unsubscribe success page
                header('Location: unsubscribe_success.php');
                exit();
            } else {
                $message = "<p class='error'>Failed to unsubscribe. Email may not be registered.</p>";
            }
        } else {
            $message = "<p class='error'>Invalid unsubscribe verification code.</p>";
            $step = 2;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unsubscribe from GitHub Updates</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h2 {
            color: #333;
            text-align: center;
            margin-bottom: 20px;
        }

        .form-section {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="email"] {
            background-color: #f8f9fa;
            color: #6c757d;
        }

        input[type="email"],
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
            margin-bottom: 15px;
        }

        button {
            background-color: #dc3545;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            width: 100%;
        }

        #submit-unsubscribe {
            background-color: #0d6efd;
            transition: background-color 0.3s ease;
        }

        #submit-unsubscribe:hover {
            background-color: #0b5ed7;
        }

        button:hover {
            background-color: #c82333;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }

        .subtitle {
            color: #64748b;
            font-size: 15px;
            text-align: center;
            line-height: 1.5;
            margin-bottom: 30px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h2>Unsubscribe</h2>
        <p class="subtitle">We're sorry to see you go. Complete the steps below to unsubscribe from GitHub updates.</p>

        <?= $message ?>

        <!-- Email Input Form - Always Visible -->
        <div class="form-section">
            <form method="POST">
                <label for="unsubscribe_email">Email Address:</label>
                <input type="email" name="unsubscribe_email" required>
                <button id="submit-unsubscribe">Send Unsubscribe Code</button>
            </form>
        </div>

        <!-- Verification Code Form - Always Visible -->
        <div class="form-section">
            <form method="POST">
                <label for="unsubscribe_verification_code">Verification Code:</label>
                <input type="text" name="unsubscribe_verification_code" placeholder="Enter 6-digit code" maxlength="6">
                <button id="verify-unsubscribe">Confirm Unsubscribe</button>
            </form>
        </div>

        <?php if ($step === 3): ?>
            <div class="form-section">
                <p style="text-align: center;">
                    <a href="index.php">← Back to Registration</a>
                </p>
            </div>
        <?php endif; ?>
    </div>
</body>

</html>