' VBScript to run the cron job silently without showing terminal windows
' This script runs the batch file in hidden mode

Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' Get the directory where this script is located
strScriptDir = objFSO.GetParentFolderName(WScript.ScriptFullName)
strBatFile = strScriptDir & "\run_cron.bat"

' Run the batch file silently (0 = hidden window, True = wait for completion)
objShell.Run """" & strBatFile & """", 0, True
