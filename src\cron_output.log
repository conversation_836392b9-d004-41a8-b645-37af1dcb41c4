[27-06-2025  1:33:26.07] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  1:33:26.27] Cron job completed 
[27-06-2025  1:38:25.90] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  1:38:26.07] Cron job completed 
[27-06-2025  1:43:26.37] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  1:43:26.52] Cron job completed 
[27-06-2025  1:48:25.94] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  1:48:26.09] Cron job completed 
[27-06-2025  2:02:22.55] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  2:02:22.97] Cron job completed 
[27-06-2025  2:03:25.80] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  2:03:25.94] Cron job completed 
[27-06-2025  2:08:25.85] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Successfully sent emails to 1 recipients
[27-06-2025  2:08:26.01] Cron job completed 
[27-06-2025  2:13:25.77] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Successfully sent emails to 1 recipients
[27-06-2025  2:13:25.97] Cron job completed 
[27-06-2025  2:18:26.04] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Successfully sent emails to 1 recipients
[27-06-2025  2:18:26.28] Cron job completed 
[27-06-2025  2:23:25.95] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Successfully sent emails to 1 recipients
[27-06-2025  2:23:26.10] Cron job completed 
[27-06-2025  2:28:25.82] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  2:28:25.99] Cron job completed 
[27-06-2025  2:33:25.96] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 3 recipients
[27-06-2025  2:33:26.19] Cron job completed 
[27-06-2025  2:38:26.26] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 3 recipients
[27-06-2025  2:38:26.46] Cron job completed 
[27-06-2025  2:43:26.02] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  2:43:26.22] Cron job completed 
[27-06-2025  2:48:26.00] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  2:48:26.15] Cron job completed 
[27-06-2025  2:53:25.85] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 3 recipients
[27-06-2025  2:53:26.02] Cron job completed 
[27-06-2025  2:58:26.10] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 5 recipients
[27-06-2025  2:58:26.60] Cron job completed 
[27-06-2025  3:03:26.53] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
PHP Warning:  mail(): Failed to connect to mailserver at "localhost" port 1025, verify your "SMTP" and "smtp_port" setting in php.ini or use ini_set() in C:\Users\<USER>\Documents\github-timeline-ParnaRoyChowdhury777\src\functions.php on line 293

Warning: mail(): Failed to connect to mailserver at "localhost" port 1025, verify your "SMTP" and "smtp_port" setting in php.ini or use ini_set() in C:\Users\<USER>\Documents\github-timeline-ParnaRoyChowdhury777\src\functions.php on line 293
Failed to send email to: <EMAIL>
[27-06-2025  3:08:26.16] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
[27-06-2025  3:13:26.07] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
PHP Warning:  mail(): Failed to connect to mailserver at "localhost" port 1025, verify your "SMTP" and "smtp_port" setting in php.ini or use ini_set() in C:\Users\<USER>\Documents\github-timeline-ParnaRoyChowdhury777\src\functions.php on line 293

Warning: mail(): Failed to connect to mailserver at "localhost" port 1025, verify your "SMTP" and "smtp_port" setting in php.ini or use ini_set() in C:\Users\<USER>\Documents\github-timeline-ParnaRoyChowdhury777\src\functions.php on line 293
Failed to send email to: <EMAIL>
PHP Warning:  mail(): Failed to connect to mailserver at "localhost" port 1025, verify your "SMTP" and "smtp_port" setting in php.ini or use ini_set() in C:\Users\<USER>\Documents\github-timeline-ParnaRoyChowdhury777\src\functions.php on line 293

Warning: mail(): Failed to connect to mailserver at "localhost" port 1025, verify your "SMTP" and "smtp_port" setting in php.ini or use ini_set() in C:\Users\<USER>\Documents\github-timeline-ParnaRoyChowdhury777\src\functions.php on line 293
Failed to send email to: <EMAIL>
PHP Warning:  mail(): Failed to connect to mailserver at "localhost" port 1025, verify your "SMTP" and "smtp_port" setting in php.ini or use ini_set() in C:\Users\<USER>\Documents\github-timeline-ParnaRoyChowdhury777\src\functions.php on line 293

Warning: mail(): Failed to connect to mailserver at "localhost" port 1025, verify your "SMTP" and "smtp_port" setting in php.ini or use ini_set() in C:\Users\<USER>\Documents\github-timeline-ParnaRoyChowdhury777\src\functions.php on line 293
Failed to send email to: <EMAIL>
[27-06-2025 11:34:33.28] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
PHP Warning:  mail(): Failed to connect to mailserver at "localhost" port 1025, verify your "SMTP" and "smtp_port" setting in php.ini or use ini_set() in C:\Users\<USER>\Documents\github-timeline-ParnaRoyChowdhury777\src\functions.php on line 293

Warning: mail(): Failed to connect to mailserver at "localhost" port 1025, verify your "SMTP" and "smtp_port" setting in php.ini or use ini_set() in C:\Users\<USER>\Documents\github-timeline-ParnaRoyChowdhury777\src\functions.php on line 293
Failed to send email to: <EMAIL>
PHP Warning:  mail(): Failed to connect to mailserver at "localhost" port 1025, verify your "SMTP" and "smtp_port" setting in php.ini or use ini_set() in C:\Users\<USER>\Documents\github-timeline-ParnaRoyChowdhury777\src\functions.php on line 293

Warning: mail(): Failed to connect to mailserver at "localhost" port 1025, verify your "SMTP" and "smtp_port" setting in php.ini or use ini_set() in C:\Users\<USER>\Documents\github-timeline-ParnaRoyChowdhury777\src\functions.php on line 293
Failed to send email to: <EMAIL>
[27-06-2025 11:38:26.06] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
PHP Warning:  mail(): Failed to connect to mailserver at "localhost" port 1025, verify your "SMTP" and "smtp_port" setting in php.ini or use ini_set() in C:\Users\<USER>\Documents\github-timeline-ParnaRoyChowdhury777\src\functions.php on line 293

Warning: mail(): Failed to connect to mailserver at "localhost" port 1025, verify your "SMTP" and "smtp_port" setting in php.ini or use ini_set() in C:\Users\<USER>\Documents\github-timeline-ParnaRoyChowdhury777\src\functions.php on line 293
Failed to send email to: <EMAIL>
[27-06-2025 11:43:26.07] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
[27-06-2025 11:48:26.14] Starting cron job 
[27-06-2025 11:53:26.12] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
[27-06-2025 11:58:26.30] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
[27-06-2025 12:03:26.27] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
[27-06-2025 12:08:26.31] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
[27-06-2025 12:13:26.13] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 9 recipients
[27-06-2025 12:13:26.25] Cron job completed 
[27-06-2025 12:18:26.21] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 6 recipients
[27-06-2025 12:18:26.34] Cron job completed 
[27-06-2025 12:23:26.30] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 4 recipients
[27-06-2025 12:23:26.44] Cron job completed 
