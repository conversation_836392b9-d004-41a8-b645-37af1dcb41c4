[27-06-2025  1:33:26.07] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  1:33:26.27] Cron job completed 
[27-06-2025  1:38:25.90] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  1:38:26.07] Cron job completed 
[27-06-2025  1:43:26.37] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  1:43:26.52] Cron job completed 
[27-06-2025  1:48:25.94] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  1:48:26.09] Cron job completed 
[27-06-2025  2:02:22.55] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  2:02:22.97] Cron job completed 
[27-06-2025  2:03:25.80] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  2:03:25.94] Cron job completed 
[27-06-2025  2:08:25.85] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Successfully sent emails to 1 recipients
[27-06-2025  2:08:26.01] Cron job completed 
[27-06-2025  2:13:25.77] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Successfully sent emails to 1 recipients
[27-06-2025  2:13:25.97] Cron job completed 
[27-06-2025  2:18:26.04] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Successfully sent emails to 1 recipients
[27-06-2025  2:18:26.28] Cron job completed 
[27-06-2025  2:23:25.95] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Successfully sent emails to 1 recipients
[27-06-2025  2:23:26.10] Cron job completed 
[27-06-2025  2:28:25.82] Starting cron job 
PHP Warning:  PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0

Warning: PHP Startup: Unable to load dynamic library 'openssl' (tried: C:\php\ext\openssl (The specified module could not be found), C:\php\ext\php_openssl.dll (The specified module could not be found)) in Unknown on line 0
Falling back to file_get_contents for GitHub API
Failed to fetch GitHub timeline with file_get_contents
Email sent successfully to: <EMAIL>
Email sent successfully to: <EMAIL>
Successfully sent emails to 2 recipients
[27-06-2025  2:28:25.99] Cron job completed 
