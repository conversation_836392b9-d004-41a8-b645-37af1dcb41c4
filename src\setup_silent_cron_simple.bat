@echo off
echo Setting up silent cron job for GitHub timeline updates...

REM Get current directory
set "CURRENT_DIR=%~dp0"
set "BAT_FILE=%CURRENT_DIR%run_cron.bat"

REM Remove existing task if it exists
schtasks /delete /tn "GitHubTimelineUpdates" /f >nul 2>&1

REM Create new silent scheduled task
echo Creating silent scheduled task...
schtasks /create /tn "GitHubTimelineUpdates" /tr "cmd /c \"%BAT_FILE%\"" /sc minute /mo 5 /ru "%USERNAME%" /f /rl highest

if %errorlevel% equ 0 (
    echo ✅ Successfully created silent scheduled task: GitHubTimelineUpdates
    echo ✅ Task will run every 5 minutes without showing terminal windows
    echo ✅ Check logs in: %CURRENT_DIR%cron_output.log
    
    REM Start the task immediately for testing
    schtasks /run /tn "GitHubTimelineUpdates"
    echo ✅ Task started for immediate testing
) else (
    echo ❌ Failed to create scheduled task
    echo Please run this script as Administrator if you encounter permission issues
)

echo.
echo To manage the task:
echo - View: schtasks /query /tn "GitHubTimelineUpdates"
echo - Stop: schtasks /end /tn "GitHubTimelineUpdates"
echo - Remove: schtasks /delete /tn "GitHubTimelineUpdates" /f
echo.
echo Logs will be written to: cron_output.log
pause
