@echo off
echo Setting up SILENT cron job for GitHub timeline updates...

REM Get current directory
set "CURRENT_DIR=%~dp0"
set "VBS_FILE=%CURRENT_DIR%run_cron_silent.vbs"

REM Remove existing task if it exists
schtasks /delete /tn "GitHubTimelineUpdates" /f >nul 2>&1

REM Create new SILENT scheduled task using VBScript
echo Creating SILENT scheduled task (no terminal popups)...
schtasks /create /tn "GitHubTimelineUpdates" /tr "wscript.exe \"%VBS_FILE%\"" /sc minute /mo 5 /ru "%USERNAME%" /f

if %errorlevel% equ 0 (
    echo ✅ Successfully created SILENT scheduled task: GitHubTimelineUpdates
    echo ✅ Task will run every 5 minutes WITHOUT showing any terminal windows
    echo ✅ Check logs in: %CURRENT_DIR%cron_output.log
    
    REM Start the task immediately for testing
    schtasks /run /tn "GitHubTimelineUpdates"
    echo ✅ Task started for immediate testing (you should see NO terminal popup)
) else (
    echo ❌ Failed to create scheduled task
    echo Please try running as Administrator if you encounter permission issues
)

echo.
echo To manage the task:
echo - View: schtasks /query /tn "GitHubTimelineUpdates"
echo - Stop: schtasks /end /tn "GitHubTimelineUpdates"  
echo - Remove: schtasks /delete /tn "GitHubTimelineUpdates" /f
echo.
echo Logs will be written to: cron_output.log
echo.
echo ✅ NO MORE TERMINAL POPUPS! The cron job now runs completely silently.
pause
