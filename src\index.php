<?php
require_once 'functions.php';
session_start();

$message = '';
$step = 1;

// Check if we're in the middle of verification process
if (isset($_SESSION['email']) && isset($_SESSION['verification_code'])) {
    $step = 2;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['email'])) {
        $email = trim($_POST['email']);

        // Check if email is already registered
        $file = __DIR__ . '/registered_emails.txt';
        if (file_exists($file)) {
            $emails = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            if (in_array($email, $emails)) {
                $message = "<p class='error'>This email is already registered for updates.</p>";
                $step = 1;
            } else {
                // Email not registered, proceed with verification
                $_SESSION['email'] = $email;
                $code = generateVerificationCode();
                $_SESSION['verification_code'] = $code;
                if (sendVerificationEmail($email, $code)) {
                    $message = "<p class='success'>Verification code sent to $email</p>";
                    $step = 2;
                } else {
                    $message = "<p class='error'>Failed to send verification code. Please try again later.</p>";
                    $step = 1;
                }
            }
        } else {
            // No registered emails file exists, proceed with verification
            $_SESSION['email'] = $email;
            $code = generateVerificationCode();
            $_SESSION['verification_code'] = $code;
            if (sendVerificationEmail($email, $code)) {
                $message = "<p class='success'>Verification code sent to $email</p>";
                $step = 2;
            } else {
                $message = "<p class='error'>Failed to send verification code. Please try again later.</p>";
                $step = 1;
            }
        }
    }

    // Handle reset request
    if (isset($_POST['reset'])) {
        unset($_SESSION['email']);
        unset($_SESSION['verification_code']);
        $message = '';
        $step = 1;
    }

    if (isset($_POST['verification_code'])) {
        error_log("Verification code submitted: " . $_POST['verification_code']);
        error_log("Session verification code: " . ($_SESSION['verification_code'] ?? 'NOT SET'));

        if ($_POST['verification_code'] === $_SESSION['verification_code']) {
            $emailToRegister = $_SESSION['email'];
            error_log("Attempting to register email: " . $emailToRegister);

            $registrationResult = registerEmail($emailToRegister);
            error_log("Registration result: " . ($registrationResult ? 'SUCCESS' : 'FAILED'));

            if ($registrationResult) {
                $_SESSION['success_email'] = $emailToRegister;
                error_log("Set success_email in session: " . $_SESSION['success_email']);

                // Clear verification session data after successful registration
                unset($_SESSION['email']);
                unset($_SESSION['verification_code']);

                error_log("About to redirect to success.php");
                // Redirect to success page
                header('Location: success.php');
                exit();
            } else {
                error_log("Registration failed for email: " . $emailToRegister);
                $message = "<p class='error'>Registration failed. Email may already be registered.</p>";
                $step = 2;
            }
        } else {
            error_log("Invalid verification code provided");
            $message = "<p class='error'>Invalid verification code.</p>";
            $step = 2;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Email Verification</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
        :root {
            --primary: #0d6efd;
            --text: #1f2937;
            --radius: 12px;
            --transition: 0.3s ease;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f6f8;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .form-card {
            background-color: #ffffff;
            padding: 40px 35px;
            border-radius: var(--radius);
            box-shadow: 0 8px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 420px;
            transition: var(--transition);
            position: relative;
        }

        h2 {
            margin-bottom: 20px;
            font-size: 1.6rem;
            color: var(--primary);
            text-align: center;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text);
            font-size: 15px;
        }

        .inputForm,
        input[type="text"],
        input[type="email"] {
            width: 100%;
            padding: 12px 14px;
            margin-bottom: 20px;
            border: 1.5px solid #ecedec;
            border-radius: 10px;
            font-size: 15px;
            box-sizing: border-box;
            transition: border var(--transition);
            background-color: rgb(249, 249, 249);
        }

        input:focus {
            border-color: var(--primary);
            outline: none;
        }

        input[readonly] {
            background-color: #f8f9fa !important;
            cursor: not-allowed !important;
            color: #6c757d;
        }

        button {
            width: 100%;
            padding: 12px;
            background-color: var(--primary);
            border: none;
            border-radius: 10px;
            color: white;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color var(--transition);
            margin-bottom: 20px;
        }

        button:hover {
            background-color: #0b5ed7;
        }

        button[name="reset"] {
            background-color: #6c757d !important;
            margin-bottom: 10px;
        }

        button[name="reset"]:hover {
            background-color: #5a6268 !important;
        }

        .form-section {
            margin-bottom: 30px;
            animation: fadeIn 0.5s ease-in-out forwards;
        }

        .form-footer {
            margin-top: 10px;
            text-align: center;
        }

        .success {
            background-color: #d1e7dd;
            padding: 10px;
            border-radius: 8px;
            color: #0f5132;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .error {
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 8px;
            color: #842029;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .subtitle {
            color: #64748b;
            font-size: 15px;
            text-align: center;
            line-height: 1.5;
            margin-bottom: 30px;
        }

        .verification_heading {
            color: #333;
            text-align: center;
            margin-bottom: 20px;
        }

        @keyframes fadeIn {
            0% {
                opacity: 0;
                transform: translateY(10px);
            }

            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 500px) {
            .form-card {
                padding: 30px 20px;
            }

            h2 {
                font-size: 1.4rem;
            }

            button {
                font-size: 15px;
            }
        }
    </style>
</head>

<body>

    <div class="form-card">
        <h2 class="verification_heading">Email Verification</h2>
        <p class="subtitle">Subscribe to get the latest Github Timeline updates delivered to your inbox.</p>
        <?= $message ?>

        <!-- Email Input Form - Always Visible -->
        <form method="POST">
            <label for="email">Email Address</label>
            <input type="email" name="email"
                value="<?= isset($_SESSION['email']) ? htmlspecialchars($_SESSION['email']) : '' ?>"
                placeholder="Enter your email"
                <?= ($step >= 2) ? 'readonly style="background-color: #f8f9fa; cursor: not-allowed;"' : '' ?>
                required />
            <?php if ($step >= 2): ?>
                <button type="submit" name="reset" style="background-color: #6c757d; margin-bottom: 10px;">Change Email</button>
            <?php else: ?>
                <button id="submit-email">Send Verification Code</button>
            <?php endif; ?>
        </form>

        <!-- Verification Code Input Form - Always Visible -->
        <form method="POST">
            <label for="verification_code">Verification Code</label>
            <input type="text" name="verification_code" placeholder="Enter 6-digit code" maxlength="6" required />
            <button id="submit-verification">Verify</button>
        </form>
    </div>

    <script>
        function backToStep1() {
            document.getElementById("step-1").classList.add("active");
            document.getElementById("step-2").classList.remove("active");
        }
    </script>

</body>

</html>