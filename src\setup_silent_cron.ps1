# PowerShell script to set up a silent cron job for GitHub timeline updates
# This script creates a Windows scheduled task that runs silently without showing terminal windows

# Get the current directory
$currentDir = Get-Location
$batFile = Join-Path $currentDir "run_cron.bat"

# Task name
$taskName = "GitHubTimelineUpdates"

Write-Host "Setting up silent cron job for GitHub timeline updates..."

# Remove existing task if it exists
try {
    Unregister-ScheduledTask -TaskName $taskName -Confirm:$false -ErrorAction SilentlyContinue
    Write-Host "Removed existing task: $taskName"
} catch {
    Write-Host "No existing task found to remove"
}

# Create the scheduled task action (runs silently)
$action = New-ScheduledTaskAction -Execute "cmd.exe" -Argument "/c `"$batFile`"" -WorkingDirectory $currentDir

# Create the trigger (every 5 minutes)
$trigger = New-ScheduledTaskTrigger -Once -At (Get-Date) -RepetitionInterval (New-TimeSpan -Minutes 5) -RepetitionDuration (New-TimeSpan -Days 365)

# Create task settings for silent execution
$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -RunOnlyIfNetworkAvailable:$false -Hidden

# Create the principal (run as current user, no interactive session)
$principal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType S4U

# Register the scheduled task
try {
    Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Settings $settings -Principal $principal -Description "Fetches GitHub timeline and sends email updates every 5 minutes (runs silently)"
    Write-Host "✅ Successfully created silent scheduled task: $taskName"
    Write-Host "✅ Task will run every 5 minutes without showing terminal windows"
    Write-Host "✅ Check logs in: $currentDir\cron_output.log"
    
    # Start the task immediately for testing
    Start-ScheduledTask -TaskName $taskName
    Write-Host "✅ Task started for immediate testing"
    
} catch {
    Write-Host "❌ Failed to create scheduled task: $($_.Exception.Message)"
    Write-Host "Please run this script as Administrator if you encounter permission issues"
}

Write-Host ""
Write-Host "To manage the task:"
Write-Host "- View: Get-ScheduledTask -TaskName '$taskName'"
Write-Host "- Stop: Stop-ScheduledTask -TaskName '$taskName'"
Write-Host "- Remove: Unregister-ScheduledTask -TaskName '$taskName' -Confirm:`$false"
Write-Host ""
Write-Host "Logs will be written to: cron_output.log"
